import { exec } from 'child_process';
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('Creando un túnel para exponer tu aplicación a Internet...');

// Puerto en el que está corriendo tu aplicación
const PORT = 3001;

// Generar un subdominio aleatorio basado en la fecha actual para evitar colisiones
const randomSubdomain = `app-${Date.now().toString(36)}`;

// Ejecutar localtunnel usando npx con un subdominio personalizado
const tunnel = exec(`npx localtunnel --port ${PORT} --subdomain ${randomSubdomain}`);

console.log(`Usando subdominio: ${randomSubdomain}`);

console.log(`Conectando al puerto ${PORT}...`);

tunnel.stdout.on('data', (data) => {
  console.log(data);

  // Buscar la URL en la salida
  if (data.includes('your url is:')) {
    console.log('\n¡Túnel creado con éxito!');
    console.log('Comparte esta URL con cualquier persona para que pueda acceder a tu aplicación.');
    console.log('Presiona Ctrl+C para detener el túnel cuando hayas terminado.');
  }
});

tunnel.stderr.on('data', (data) => {
  console.error(`Error: ${data}`);
});

// Manejar la salida del proceso
tunnel.on('close', (code) => {
  console.log(`El túnel se ha cerrado con código ${code}`);
  rl.close();
});

// Permitir que el usuario detenga el túnel con Ctrl+C
rl.on('SIGINT', () => {
  console.log('\nDeteniendo el túnel...');
  tunnel.kill();
  rl.close();
});
