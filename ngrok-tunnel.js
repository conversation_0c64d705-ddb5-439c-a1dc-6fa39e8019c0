const { exec } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('Creando un túnel con ngrok para exponer tu aplicación a Internet...');

// Puerto en el que está corriendo tu aplicación
const PORT = 3001;

// Ejecutar ngrok
const tunnel = exec(`ngrok http ${PORT}`);

console.log(`Conectando al puerto ${PORT}...`);
console.log('Buscando la URL pública en la salida de ngrok...');

tunnel.stdout.on('data', (data) => {
  console.log(data);
});

tunnel.stderr.on('data', (data) => {
  console.error(`Error: ${data}`);
});

// Manejar la salida del proceso
tunnel.on('close', (code) => {
  console.log(`El túnel se ha cerrado con código ${code}`);
  rl.close();
});

// Permitir que el usuario detenga el túnel con Ctrl+C
rl.on('SIGINT', () => {
  console.log('\nDeteniendo el túnel...');
  tunnel.kill();
  rl.close();
});
