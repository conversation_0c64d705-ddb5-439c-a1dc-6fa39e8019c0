import fetch from 'node-fetch';

async function getNgrokUrl() {
  try {
    const response = await fetch('http://127.0.0.1:4040/api/tunnels');
    const data = await response.json();
    
    if (data.tunnels && data.tunnels.length > 0) {
      const publicUrl = data.tunnels[0].public_url;
      console.log('¡URL pública de ngrok encontrada!');
      console.log('--------------------------------');
      console.log(`URL: ${publicUrl}`);
      console.log('--------------------------------');
      console.log('Comparte esta URL con cualquier persona para que pueda acceder a tu aplicación.');
      console.log('Esta URL estará activa mientras mantengas ngrok en ejecución.');
    } else {
      console.log('No se encontraron túneles activos de ngrok.');
    }
  } catch (error) {
    console.error('Error al obtener la URL de ngrok:', error.message);
    console.log('Asegúrate de que ngrok esté en ejecución y que la interfaz web esté disponible en http://127.0.0.1:4040');
  }
}

getNgrokUrl();
