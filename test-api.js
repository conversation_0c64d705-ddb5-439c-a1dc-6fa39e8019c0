/**
 * Script para probar la API del sistema
 * 
 * Uso: node test-api.js
 */

import dotenv from 'dotenv';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import FormData from 'form-data';

// Cargar variables de entorno
dotenv.config();

const API_URL = 'http://localhost:3001/api';

async function testAPI() {
  try {
    console.log('=== Prueba de API ===');
    
    // 1. Verificar que el servidor está en funcionamiento
    console.log('\n1. Verificando estado del servidor...');
    const healthResponse = await axios.get(`${API_URL}/health`);
    console.log(`Estado: ${healthResponse.data.status}`);
    console.log(`Timestamp: ${healthResponse.data.timestamp}`);
    console.log(`OpenAI configurado: ${healthResponse.data.openaiConfigured ? 'Sí' : 'No'}`);
    
    // 2. Obtener lista de reuniones
    console.log('\n2. Obteniendo lista de reuniones...');
    const meetingsResponse = await axios.get(`${API_URL}/meetings`);
    console.log(`Reuniones encontradas: ${meetingsResponse.data.meetings.length}`);
    
    if (meetingsResponse.data.meetings.length > 0) {
      const lastMeeting = meetingsResponse.data.meetings[meetingsResponse.data.meetings.length - 1];
      console.log(`Última reunión: ${lastMeeting.meetingId}`);
      console.log(`Estado: ${lastMeeting.status}`);
      
      // 3. Obtener detalles de la última reunión
      console.log('\n3. Obteniendo detalles de la última reunión...');
      const meetingDetailsResponse = await axios.get(`${API_URL}/meetings/${lastMeeting.meetingId}`);
      console.log(`Detalles obtenidos: ${meetingDetailsResponse.data.success ? 'Sí' : 'No'}`);
      console.log(`Estado: ${meetingDetailsResponse.data.status}`);
      
      // 4. Obtener transcripción de la última reunión
      if (lastMeeting.hasTranscription) {
        console.log('\n4. Obteniendo transcripción...');
        const transcriptionResponse = await axios.get(`${API_URL}/meetings/${lastMeeting.meetingId}/transcription`);
        console.log(`Transcripción obtenida: ${transcriptionResponse.data.success ? 'Sí' : 'No'}`);
        if (transcriptionResponse.data.success && transcriptionResponse.data.transcription.text) {
          console.log(`Texto (primeros 100 caracteres): ${transcriptionResponse.data.transcription.text.substring(0, 100)}...`);
        }
      }
    }
    
    // 5. Subir un archivo de audio (opcional)
    const shouldUploadFile = process.argv.includes('--upload');
    if (shouldUploadFile) {
      console.log('\n5. Subiendo archivo de audio...');
      
      // Buscar un archivo de audio en el directorio storage/audio
      const audioDir = path.join(process.cwd(), 'storage', 'audio');
      const audioFiles = fs.readdirSync(audioDir).filter(file => 
        file.endsWith('.mp3') || file.endsWith('.wav') || file.endsWith('.m4a')
      );
      
      if (audioFiles.length === 0) {
        console.log('No se encontraron archivos de audio para subir.');
      } else {
        const audioFile = path.join(audioDir, audioFiles[0]);
        console.log(`Subiendo archivo: ${audioFile}`);
        
        const formData = new FormData();
        formData.append('audio', fs.createReadStream(audioFile));
        
        const uploadResponse = await axios.post(`${API_URL}/meetings/upload`, formData, {
          headers: formData.getHeaders(),
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        });
        
        console.log(`Archivo subido: ${uploadResponse.data.success ? 'Sí' : 'No'}`);
        console.log(`ID de reunión: ${uploadResponse.data.meetingId}`);
        console.log(`Estado: ${uploadResponse.data.status}`);
      }
    }
    
    console.log('\nPrueba de API completada con éxito');
  } catch (error) {
    console.error('Error durante la prueba de API:');
    if (error.response) {
      console.error(`Estado: ${error.response.status}`);
      console.error('Respuesta:', error.response.data);
    } else {
      console.error(error);
    }
  }
}

// Ejecutar prueba
testAPI();
