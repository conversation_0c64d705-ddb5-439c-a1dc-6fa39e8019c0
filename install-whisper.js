/**
 * Script para instalar Whisper CLI
 * 
 * Este script verifica si Whisper CLI está instalado y proporciona instrucciones para instalarlo.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { getWhisperInstallationInstructions } from './src/services/whisper-cli.js';

const execAsync = promisify(exec);

/**
 * Verifica si Whisper CLI está instalado
 * @returns {Promise<boolean>} - true si Whisper está instalado, false en caso contrario
 */
async function isWhisperInstalled() {
  try {
    await execAsync('whisper --help');
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Verifica si Python está instalado
 * @returns {Promise<boolean>} - true si Python está instalado, false en caso contrario
 */
async function isPythonInstalled() {
  try {
    await execAsync('python --version');
    return true;
  } catch (error) {
    try {
      await execAsync('python3 --version');
      return true;
    } catch (error2) {
      return false;
    }
  }
}

/**
 * Verifica si FFmpeg está instalado
 * @returns {Promise<boolean>} - true si FFmpeg está instalado, false en caso contrario
 */
async function isFFmpegInstalled() {
  try {
    await execAsync('ffmpeg -version');
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Función principal
 */
async function main() {
  console.log('Verificando requisitos para Whisper CLI...');
  
  // Verificar si Whisper CLI ya está instalado
  const whisperInstalled = await isWhisperInstalled();
  if (whisperInstalled) {
    console.log('✅ Whisper CLI ya está instalado en el sistema.');
    process.exit(0);
  }
  
  console.log('❌ Whisper CLI no está instalado.');
  
  // Verificar si Python está instalado
  const pythonInstalled = await isPythonInstalled();
  if (pythonInstalled) {
    console.log('✅ Python está instalado.');
  } else {
    console.log('❌ Python no está instalado. Debes instalar Python 3.7 o superior.');
    console.log('   Visita https://www.python.org/downloads/ para descargar e instalar Python.');
  }
  
  // Verificar si FFmpeg está instalado
  const ffmpegInstalled = await isFFmpegInstalled();
  if (ffmpegInstalled) {
    console.log('✅ FFmpeg está instalado.');
  } else {
    console.log('❌ FFmpeg no está instalado. Debes instalar FFmpeg para procesar archivos de audio.');
    console.log('   Visita https://ffmpeg.org/download.html para descargar e instalar FFmpeg.');
  }
  
  // Mostrar instrucciones de instalación
  console.log('\nInstrucciones para instalar Whisper CLI:');
  console.log(getWhisperInstallationInstructions());
}

// Ejecutar la función principal
main().catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
