# Configuración del servidor
PORT=3001
NODE_ENV=development

# Rutas de almacenamiento
AUDIO_STORAGE_PATH=./storage/audio
TRANSCRIPTION_STORAGE_PATH=./storage/transcriptions

# Claves API (reemplazar con tus propias claves)
# GOOGLE_APPLICATION_CREDENTIALS=path/to/your/credentials.json

# Para usar OpenAI, necesitas obtener una clave API de https://platform.openai.com/api-keys
# y reemplazar 'your_openai_api_key' con tu clave real
OPENAI_API_KEY=your_openai_api_key

# Configuración de Whisper
WHISPER_MODEL=tiny  # Opciones: tiny, base, small, medium, large
