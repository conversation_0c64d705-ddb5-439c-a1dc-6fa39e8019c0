{"name": "walter-meeting", "private": true, "version": "0.1.0", "type": "module", "scripts": {"start": "node src/api/index.js", "dev": "nodemon src/api/index.js", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@google-cloud/speech": "^7.1.0", "chart.js": "^4.4.1", "cors": "^2.8.5", "docx": "^8.5.0", "dotenv": "^16.4.5", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "natural": "^6.10.4", "node-nlp": "^4.27.0", "nodejs-whisper": "^0.2.9", "openai": "^4.98.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.1.0"}}