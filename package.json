{"name": "walter-meeting", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "node src/api/index.js", "dev:server": "nodemon src/api/index.js", "dev:ui": "vite", "dev:all": "concurrently \"npm run dev:server\" \"npm run dev:ui\"", "check-whisper": "node install-whisper.js", "test:transcription": "node test-transcription.js", "test:meeting": "node test-meeting-processing.js", "test:api": "node test-api.js", "test:api:upload": "node test-api.js --upload", "test:health": "curl http://localhost:3001/api/health"}, "dependencies": {"@google-cloud/speech": "^6.3.0", "axios": "^1.6.7", "chart.js": "^4.4.1", "cors": "^2.8.5", "docx": "^8.5.0", "dotenv": "^16.4.5", "express": "^4.18.2", "ffmpeg-static": "^5.2.0", "multer": "^1.4.5-lts.1", "natural": "^6.10.4", "node-nlp": "^4.27.0", "nodejs-whisper": "^0.2.6", "openai": "^4.98.0", "uuid": "^9.0.1"}, "devDependencies": {"concurrently": "^8.2.2", "jest": "^29.7.0", "nodemon": "^3.1.0", "prettier": "^3.3.1", "vite": "^5.1.6"}}