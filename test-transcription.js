/**
 * Script para probar la transcripción con OpenAI
 * 
 * Uso: node test-transcription.js <ruta-al-archivo-de-audio>
 */

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { transcribeAudio } from './src/services/openai.js';

// Cargar variables de entorno
dotenv.config();

async function testTranscription() {
  try {
    // Verificar argumentos
    if (process.argv.length < 3) {
      console.error('Uso: node test-transcription.js <ruta-al-archivo-de-audio>');
      process.exit(1);
    }

    const audioFilePath = process.argv[2];
    
    // Verificar que el archivo existe
    if (!fs.existsSync(audioFilePath)) {
      console.error(`El archivo no existe: ${audioFilePath}`);
      process.exit(1);
    }

    console.log('=== Prueba de Transcripción con OpenAI ===');
    console.log(`Archivo de audio: ${audioFilePath}`);
    console.log(`Tamaño: ${fs.statSync(audioFilePath).size} bytes`);
    console.log(`Clave API de OpenAI configurada: ${process.env.OPENAI_API_KEY ? 'Sí' : 'No'}`);
    
    if (!process.env.OPENAI_API_KEY) {
      console.error('No se ha configurado la clave API de OpenAI en el archivo .env');
      process.exit(1);
    }

    console.log('\nIniciando transcripción...');
    const startTime = Date.now();
    
    const transcription = await transcribeAudio(audioFilePath);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`\nTranscripción completada en ${duration.toFixed(2)} segundos`);
    console.log('Resultado:');
    console.log('-----------------------------------');
    console.log(`Texto: ${transcription.text.substring(0, 200)}...`);
    console.log(`Segmentos: ${transcription.segments.length}`);
    
    if (transcription.filePath) {
      console.log(`\nTranscripción guardada en: ${transcription.filePath}`);
    }
    
    console.log('\nPrueba completada con éxito');
  } catch (error) {
    console.error('Error durante la prueba de transcripción:');
    console.error(error);
    process.exit(1);
  }
}

testTranscription();
