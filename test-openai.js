/**
 * Script para probar la API de OpenAI directamente
 * 
 * Uso: node test-openai.js <ruta-al-archivo-de-audio>
 * Ejemplo: node test-openai.js storage/audio/audio_1746811963202.mp3
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import OpenAI from 'openai';

// Cargar variables de entorno
dotenv.config();

// Obtener la ruta del archivo de audio
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Verificar argumentos
if (process.argv.length < 3) {
  console.error('Uso: node test-openai.js <ruta-al-archivo-de-audio>');
  process.exit(1);
}

const audioFilePath = process.argv[2];
const fullPath = path.resolve(process.cwd(), audioFilePath);

// Verificar que el archivo existe
if (!fs.existsSync(fullPath)) {
  console.error(`El archivo de audio no existe: ${fullPath}`);
  process.exit(1);
}

// Verificar que el archivo es accesible
const stats = fs.statSync(fullPath);
console.log(`Archivo de audio: ${fullPath}`);
console.log(`Tamaño del archivo: ${stats.size} bytes`);

if (stats.size === 0) {
  console.error('El archivo de audio está vacío');
  process.exit(1);
}

// Verificar la clave API de OpenAI
if (!process.env.OPENAI_API_KEY) {
  console.error('No se ha configurado la clave API de OpenAI');
  process.exit(1);
}

console.log(`Clave API de OpenAI configurada: ${process.env.OPENAI_API_KEY ? 'Sí' : 'No'}`);
console.log(`Longitud de la clave API: ${process.env.OPENAI_API_KEY.length}`);
console.log(`Primeros 10 caracteres de la clave API: ${process.env.OPENAI_API_KEY.substring(0, 10)}...`);

// Crear cliente de OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Función para transcribir el audio
async function transcribeAudio() {
  try {
    console.log('Transcribiendo audio con OpenAI...');
    console.log('Creando stream de lectura para el archivo...');
    
    // Crear un stream de lectura para el archivo
    const audioFile = fs.createReadStream(fullPath);
    
    console.log('Enviando solicitud a OpenAI...');
    console.log('Modelo: whisper-1, Idioma: es');
    
    // Llamar a la API de OpenAI para transcribir el audio
    const response = await openai.audio.transcriptions.create({
      file: audioFile,
      model: 'whisper-1',
      language: 'es',
      response_format: 'verbose_json'
    });
    
    console.log('Transcripción completada con OpenAI');
    console.log('Respuesta recibida:', response);
    
    if (!response || !response.text) {
      console.error('La respuesta de OpenAI no contiene texto transcrito');
      return;
    }
    
    console.log(`Texto transcrito: ${response.text.substring(0, 100)}...`);
    
    // Guardar la transcripción en un archivo
    const transcriptionPath = path.join(__dirname, 'transcription.json');
    fs.writeFileSync(transcriptionPath, JSON.stringify(response, null, 2));
    console.log(`Transcripción guardada en: ${transcriptionPath}`);
    
    return response;
  } catch (error) {
    console.error('Error al transcribir audio con OpenAI:', error);
    console.error('Detalles del error:', error.message);
    if (error.response) {
      console.error('Respuesta de error de OpenAI:', error.response.data);
    }
    throw error;
  }
}

// Ejecutar la transcripción
transcribeAudio()
  .then(() => {
    console.log('Transcripción completada exitosamente');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error durante la transcripción:', error);
    process.exit(1);
  });
