# Sistema de Agentes para Transcripción y Análisis de Reuniones

Este sistema utiliza agentes de IA para transcribir, analizar, resumir y planificar a partir de grabaciones de reuniones.

## Características

- **Transcripción automática** de archivos de audio usando OpenAI Whisper
- **An<PERSON>lisis de contenido** para identificar temas clave, sentimiento, preguntas y objetivos
- **Resumen automático** de la reunión con puntos clave
- **Seguimiento de objetivos y tareas** mencionados en la reunión
- **Generación de planes** con cartas Gantt basados en los objetivos identificados
- **Interfaz de usuario** moderna y fácil de usar

## Requisitos

- Node.js 18 o superior
- Clave API de OpenAI (para transcripción y análisis)

## Configuración

1. Clona este repositorio
2. Instala las dependencias: `npm install`
3. Copia el archivo `.env.example` a `.env` y configura tus claves API:

```
OPENAI_API_KEY=tu_clave_api_de_openai
```

## Uso

### Iniciar el servidor

```bash
npm start
```

Esto iniciará el servidor en http://localhost:3001

### Iniciar el modo desarrollo

```bash
npm run dev:all
```

Esto iniciará tanto el servidor API como la interfaz de usuario en modo desarrollo.

### Probar la transcripción

```bash
npm run test:transcription ruta/al/archivo/audio.mp3
```

### Probar el procesamiento completo

```bash
npm run test:meeting ruta/al/archivo/audio.mp3
```

## Flujo de trabajo

1. Sube un archivo de audio de una reunión a través de la interfaz web o la API
2. El sistema transcribe automáticamente el audio a texto
3. El texto se analiza para identificar temas clave, preguntas, respuestas y objetivos
4. Se genera un resumen conciso de la reunión
5. Se identifican y organizan las tareas y objetivos
6. Se crea un plan con carta Gantt para visualizar las tareas y plazos

## Tecnologías utilizadas

- Node.js y Express para el backend
- JavaScript moderno (ES6+)
- OpenAI API para transcripción y análisis
- Chart.js para visualización de datos
- Arquitectura basada en agentes para modularidad

## Instalación

1. Clona este repositorio:
```
git clone https://github.com/tu-usuario/sistema-agentes-reuniones.git
cd sistema-agentes-reuniones
```

2. Instala las dependencias:
```
npm install
```

3. Crea un archivo `.env` basado en `.env.example`:
```
cp .env.example .env
```

4. Edita el archivo `.env` para configurar las variables de entorno:
   - Para usar OpenAI, obtén una clave API de [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys) y actualiza `OPENAI_API_KEY`
   - Configura el modelo de Whisper que deseas usar (`tiny`, `base`, `small`, `medium`, `large`)

5. (Opcional) Para obtener la mejor calidad de transcripción, instala Whisper CLI:
   - Verifica si Whisper CLI está instalado:
   ```
   npm run check-whisper
   ```
   - Si no está instalado, sigue las instrucciones proporcionadas para instalar Python, FFmpeg y Whisper

## Uso

1. Inicia el sistema:
```
npm run dev:all
```

2. Abre tu navegador y accede a:
```
http://localhost:3001
```

3. Sube un archivo de audio de una reunión
4. Explora los resultados:
   - Transcripción
   - Resumen
   - Análisis
   - Plan y carta Gantt

## Estructura del Proyecto

- `src/agents/`: Agentes especializados para cada tarea
  - `transcriber/`: Transcripción de audio a texto
  - `analyzer/`: Análisis de contenido
  - `summarizer/`: Generación de resúmenes
  - `tracker/`: Seguimiento de objetivos y tareas
  - `planner/`: Generación de planes
- `src/api/`: API RESTful para interactuar con los agentes
- `src/core/`: Componentes centrales del sistema
- `src/ui/`: Interfaz de usuario
- `storage/`: Almacenamiento de archivos de audio y resultados

## Licencia

MIT
