/**
 * Script para probar el procesamiento completo de una reunión
 * 
 * Uso: node test-meeting-processing.js <ruta-al-archivo-de-audio>
 */

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import Coordinator from './src/core/coordinator.js';

// Cargar variables de entorno
dotenv.config();

async function testMeetingProcessing() {
  try {
    // Verificar argumentos
    if (process.argv.length < 3) {
      console.error('Uso: node test-meeting-processing.js <ruta-al-archivo-de-audio>');
      process.exit(1);
    }

    const audioFilePath = process.argv[2];
    
    // Verificar que el archivo existe
    if (!fs.existsSync(audioFilePath)) {
      console.error(`El archivo no existe: ${audioFilePath}`);
      process.exit(1);
    }

    console.log('=== Prueba de Procesamiento Completo de Reunión ===');
    console.log(`Archivo de audio: ${audioFilePath}`);
    console.log(`Tamaño: ${fs.statSync(audioFilePath).size} bytes`);
    console.log(`Clave API de OpenAI configurada: ${process.env.OPENAI_API_KEY ? 'Sí' : 'No'}`);
    
    if (!process.env.OPENAI_API_KEY) {
      console.error('No se ha configurado la clave API de OpenAI en el archivo .env');
      process.exit(1);
    }

    // Crear coordinador
    console.log('\nCreando coordinador...');
    const coordinator = new Coordinator({
      transcriber: {
        useWhisperCLI: false,  // Preferir OpenAI API
        useWhisper: true,      // Fallback a Whisper local
        useGoogleSpeech: false // No usar Google Speech
      }
    });

    // Configurar eventos para seguimiento
    coordinator.on('processing:start', ({ meetingId }) => {
      console.log(`\nIniciando procesamiento de reunión: ${meetingId}`);
    });

    coordinator.on('processing:transcription:start', () => {
      console.log('Iniciando transcripción...');
    });

    coordinator.on('processing:transcription:complete', () => {
      console.log('Transcripción completada');
    });

    coordinator.on('processing:analysis:start', () => {
      console.log('Iniciando análisis...');
    });

    coordinator.on('processing:analysis:complete', () => {
      console.log('Análisis completado');
    });

    coordinator.on('processing:summary:start', () => {
      console.log('Iniciando resumen...');
    });

    coordinator.on('processing:summary:complete', () => {
      console.log('Resumen completado');
    });

    coordinator.on('processing:tracking:start', () => {
      console.log('Iniciando seguimiento...');
    });

    coordinator.on('processing:tracking:complete', () => {
      console.log('Seguimiento completado');
    });

    coordinator.on('processing:planning:start', () => {
      console.log('Iniciando planificación...');
    });

    coordinator.on('processing:planning:complete', () => {
      console.log('Planificación completada');
    });

    coordinator.on('processing:complete', ({ meetingId }) => {
      console.log(`\nProcesamiento completado para reunión: ${meetingId}`);
    });

    coordinator.on('processing:failed', ({ meetingId, error }) => {
      console.error(`\nError en el procesamiento de la reunión ${meetingId}: ${error}`);
    });

    // Iniciar procesamiento
    console.log('\nIniciando procesamiento...');
    const startTime = Date.now();
    
    const result = await coordinator.processMeeting(audioFilePath);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`\nProcesamiento completado en ${duration.toFixed(2)} segundos`);
    console.log(`Estado final: ${result.status}`);
    
    if (result.status === 'completed') {
      console.log('\nResumen de resultados:');
      console.log('-----------------------------------');
      console.log(`Transcripción: ${result.steps.transcription.success ? 'Exitosa' : 'Fallida'}`);
      console.log(`Análisis: ${result.steps.analysis.success ? 'Exitoso' : 'Fallido'}`);
      console.log(`Resumen: ${result.steps.summary.success ? 'Exitoso' : 'Fallido'}`);
      console.log(`Seguimiento: ${result.steps.tracking.success ? 'Exitoso' : 'Fallido'}`);
      console.log(`Planificación: ${result.steps.planning.success ? 'Exitosa' : 'Fallida'}`);
      
      // Mostrar parte del texto transcrito
      if (result.steps.transcription.success && result.steps.transcription.transcription.text) {
        console.log('\nTexto transcrito (primeros 200 caracteres):');
        console.log(result.steps.transcription.transcription.text.substring(0, 200) + '...');
      }
      
      // Mostrar objetivos identificados
      if (result.steps.tracking.success && result.steps.tracking.tracking.objectives) {
        console.log('\nObjetivos identificados:');
        result.steps.tracking.tracking.objectives.forEach(obj => {
          console.log(`- ${obj.text}`);
        });
      }
    } else {
      console.error(`\nError en el procesamiento: ${result.error}`);
    }
    
    console.log('\nPrueba completada');
  } catch (error) {
    console.error('Error durante la prueba de procesamiento:');
    console.error(error);
    process.exit(1);
  }
}

testMeetingProcessing();
