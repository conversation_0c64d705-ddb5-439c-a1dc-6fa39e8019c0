import http from 'http';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PORT = 8080;

const server = http.createServer(async (req, res) => {
  // Ruta por defecto
  let filePath = req.url;

  // Eliminar cualquier parámetro de consulta
  filePath = filePath.split('?')[0];

  // Manejar la ruta raíz
  if (filePath === '/') {
    filePath = '/index.html';
  }

  // Manejar solicitudes a la API
  if (filePath.startsWith('/api/')) {
    // Redirigir solicitudes a la API al servidor en el puerto 3001
    console.log(`Redirigiendo solicitud a la API: ${filePath}`);

    // Crear un proxy para la solicitud a la API
    const apiUrl = new URL(`http://localhost:3001${filePath}`);
    console.log(`Redirigiendo a: ${apiUrl}`);

    // Configurar opciones para la solicitud
    const options = {
      hostname: apiUrl.hostname,
      port: apiUrl.port,
      path: apiUrl.pathname + apiUrl.search,
      method: req.method,
      headers: req.headers,
      timeout: 60000 // Aumentar el timeout a 60 segundos
    };

    // Asegurarse de que el host sea correcto para la solicitud interna
    if (options.headers && options.headers.host) {
      options.headers.host = apiUrl.host;
    }

    // Crear una promesa para manejar la solicitud
    const proxyRequest = new Promise((resolve, reject) => {
      const proxyReq = http.request(options, (proxyRes) => {
        // Copiar las cabeceras de la respuesta
        res.writeHead(proxyRes.statusCode, proxyRes.headers);

        // Reenviar los datos de la respuesta
        proxyRes.pipe(res);

        // Cuando la respuesta termine, resolver la promesa
        proxyRes.on('end', () => {
          resolve();
        });
      });

      // Manejar errores
      proxyReq.on('error', (error) => {
        console.error(`Error al redirigir a la API: ${error.message}`);
        res.writeHead(500);
        res.end(`Error al comunicarse con la API: ${error.message}`);
        reject(error);
      });

      // Si hay datos en la solicitud original, reenviarlos
      if (req.method !== 'GET' && req.method !== 'HEAD') {
        req.pipe(proxyReq);
      } else {
        proxyReq.end();
      }
    });

    try {
      await proxyRequest;
      return; // Terminar el manejo de la solicitud aquí
    } catch (error) {
      // El error ya se manejó en el callback de error
      return;
    }
  }

  // Manejar rutas para archivos estáticos
  if (filePath.startsWith('/css/') ||
      filePath.startsWith('/js/') ||
      filePath.startsWith('/images/') ||
      filePath.startsWith('/fonts/')) {
    // Servir archivos estáticos desde el directorio public
    filePath = './public' + filePath;
  } else if (filePath.startsWith('/src/')) {
    // Servir archivos de código fuente
    filePath = '.' + filePath;
  } else if (filePath.startsWith('/')) {
    // Otros archivos en la raíz
    filePath = '.' + filePath;
  } else {
    filePath = './' + filePath;
  }

  // Obtener la extensión del archivo
  const extname = String(path.extname(filePath)).toLowerCase();

  // Tipos MIME
  const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp3': 'audio/mpeg',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm'
  };

  const contentType = mimeTypes[extname] || 'application/octet-stream';

  console.log(`Solicitando archivo: ${filePath}`);

  // Leer el archivo
  fs.readFile(filePath, (error, content) => {
    if (error) {
      console.error(`Error al leer el archivo ${filePath}: ${error.code}`);

      if (error.code === 'ENOENT') {
        // Página no encontrada
        console.log(`Archivo no encontrado: ${filePath}, redirigiendo a index.html`);

        fs.readFile('./index.html', (err, content) => {
          if (err) {
            console.error(`Error al leer index.html: ${err.code}`);
            res.writeHead(500);
            res.end('Error interno del servidor');
            return;
          }

          res.writeHead(200, { 'Content-Type': 'text/html' });
          res.end(content, 'utf-8');
        });
      } else {
        // Error del servidor
        res.writeHead(500);
        res.end('Error interno del servidor: ' + error.code);
      }
    } else {
      // Éxito
      console.log(`Sirviendo archivo: ${filePath} (${contentType})`);
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(content, 'utf-8');
    }
  });
});

server.listen(PORT, () => {
  console.log(`Servidor ejecutándose en http://localhost:${PORT}`);
});
